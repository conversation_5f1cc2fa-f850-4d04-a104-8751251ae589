# iPerf3 控制程序技术设计清单

## 项目概述
- **目标平台**: OpenWRT ARM64
- **核心功能**: 控制iperf3测试12台服务器到OpenWRT的下行速度
- **协议支持**: TCP和UDP
- **性能要求**: 高性能、低资源占用、并发优化

## 1. 技术栈选择

### 1.1 编程语言
- [x] **Go 1.21+**
  - 原生ARM64支持
  - 静态编译，单文件部署
  - 优秀的并发性能
  - 小内存占用

### 1.2 核心依赖
```go
// 最小化依赖原则
github.com/spf13/cobra    // CLI框架
github.com/spf13/viper    // 配置管理  
github.com/sirupsen/logrus // 日志系统
gopkg.in/yaml.v3          // YAML解析
```

### 1.3 避免的依赖
- [ ] 重型Web框架 (Gin, Echo等)
- [ ] ORM框架 (GORM等)
- [ ] 图形界面库

## 2. 架构设计

### 2.1 分层架构
```
├── cmd/                    # 应用入口
├── internal/              # 内部实现
│   ├── app/              # 应用层
│   ├── domain/           # 领域层
│   ├── infrastructure/   # 基础设施层
│   └── interfaces/       # 接口层
├── pkg/                  # 公共包
└── configs/              # 配置文件
```

### 2.2 核心模块
- [x] **主控制器** (Controller): 任务调度和流程控制
- [x] **测试执行器** (Executor): iperf3命令封装和执行
- [x] **工作池** (WorkerPool): 并发控制和资源管理
- [x] **结果处理器** (ResultHandler): 数据收集和分析
- [x] **配置管理器** (ConfigManager): 参数和服务器管理
- [x] **监控模块** (Monitor): 系统资源监控

## 3. 性能优化策略

### 3.1 并发控制
- [x] **分批测试**: 12台服务器分成3-4批
- [x] **错峰启动**: 时间偏移避免同时开始
- [x] **自适应并发**: 根据系统负载动态调整
- [x] **Worker Pool模式**: 控制goroutine数量

### 3.2 iperf3参数优化
#### TCP测试参数
```bash
iperf3 -c <server> -P 4 -w 512K -M 1460 -t 30 --reverse --get-server-output -f m
```
- `-P 4`: 4个并行流
- `-w 512K`: TCP窗口大小
- `-M 1460`: MSS大小
- `-t 30`: 测试30秒
- `--reverse`: 反向测试(服务器发送)

#### UDP测试参数  
```bash
iperf3 -c <server> -u -b 500M -l 1472 -t 10 --reverse -f m
```
- `-u`: UDP模式
- `-b 500M`: 目标带宽500Mbps
- `-l 1472`: UDP包大小(避免分片)
- `-t 10`: 测试10秒

### 3.3 系统级优化
```bash
# 网络缓冲区优化
echo 134217728 > /proc/sys/net/core/rmem_max
echo 134217728 > /proc/sys/net/core/wmem_max

# 文件描述符限制
ulimit -n 65536

# CPU绑定
taskset -c 0-3 ./iperf3-controller
```

## 4. 核心接口设计

### 4.1 测试执行器接口
```go
type TestExecutor interface {
    ExecuteTCP(ctx context.Context, target string, params TCPParams) (*TestResult, error)
    ExecuteUDP(ctx context.Context, target string, params UDPParams) (*TestResult, error)
    Validate(target string) error
}
```

### 4.2 工作池接口
```go
type WorkerPool interface {
    Submit(task *TestTask) error
    SetConcurrency(n int)
    Start(ctx context.Context) error
    Stop() error
    Results() <-chan *TestResult
}
```

### 4.3 结果处理接口
```go
type ResultHandler interface {
    Collect(result *TestResult) error
    Aggregate() (*AggregatedResult, error)
    GenerateReport(format string) ([]byte, error)
    Export(path string) error
}
```

## 5. 配置管理

### 5.1 服务器配置
```yaml
servers:
  - name: "server-01"
    host: "************"
    port: 5201
    enabled: true
  - name: "server-02" 
    host: "************"
    port: 5201
    enabled: true
```

### 5.2 性能配置
```yaml
performance:
  tcp:
    parallel_streams: 4
    window_size: "512K"
    mss: 1460
    duration: 30
  udp:
    bandwidth: "500M"
    packet_size: 1472
    duration: 10
  concurrency:
    max_workers: 4
    batch_size: 3
    batch_delay: "2s"
```

## 6. 交叉编译和部署

### 6.1 编译命令
```bash
CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build \
  -ldflags="-s -w" \
  -trimpath \
  -o iperf3-controller-arm64 \
  ./cmd/iperf3-controller
```

### 6.2 部署文件结构
```
/opt/iperf3-controller/
├── iperf3-controller-arm64    # 主程序
├── config.yaml               # 配置文件
├── servers.yaml              # 服务器列表
└── logs/                     # 日志目录
```

## 7. 监控和日志

### 7.1 关键指标
- 测试成功率
- 平均吞吐量
- 延迟统计
- 系统资源使用率
- 错误率和类型

### 7.2 日志级别
- ERROR: 系统错误和测试失败
- WARN: 性能警告和重试
- INFO: 测试进度和结果
- DEBUG: 详细执行信息

## 8. 错误处理和恢复

### 8.1 重试策略
- 网络超时: 最多重试3次
- 连接失败: 指数退避重试
- 资源不足: 降低并发数重试

### 8.2 故障恢复
- 单个服务器失败不影响其他测试
- 系统资源不足时自动降级
- 异常退出时保存已完成的结果

## 9. 测试策略

### 9.1 单元测试
- 各模块接口测试
- 参数解析和验证
- 错误处理逻辑

### 9.2 集成测试  
- 端到端测试流程
- 并发场景测试
- 资源限制测试

### 9.3 性能测试
- 内存使用测试
- CPU占用测试
- 网络性能测试

## 10. 审查检查点

### 10.1 技术选择
- [ ] Go语言是否适合OpenWRT ARM64环境？
- [ ] 依赖库是否过重？
- [ ] 是否需要CGO支持？

### 10.2 架构设计
- [ ] 模块划分是否合理？
- [ ] 接口设计是否清晰？
- [ ] 是否考虑了扩展性？

### 10.3 性能优化
- [ ] 并发策略是否合适？
- [ ] iperf3参数是否最优？
- [ ] 系统优化是否充分？

### 10.4 部署运维
- [ ] 交叉编译是否正确？
- [ ] 配置管理是否灵活？
- [ ] 监控指标是否完整？

## 11. 风险评估

### 11.1 技术风险
- ARM64兼容性问题
- 内存泄漏风险
- 网络超时处理

### 11.2 性能风险
- 带宽竞争导致结果不准确
- 系统资源不足导致测试失败
- 并发过高导致系统不稳定

### 11.3 缓解措施
- 充分的测试验证
- 渐进式部署
- 完善的监控和告警

---

**请审查以上设计清单，重点关注：**
1. 技术栈选择是否合适？
2. 架构设计是否满足需求？
3. 性能优化策略是否充分？
4. 是否有遗漏的重要考虑点？
