package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

var (
	version = "dev"
	commit  = "unknown"
	date    = "unknown"
)

func main() {
	if err := newRootCommand().Execute(); err != nil {
		logrus.Fatal(err)
	}
}

func newRootCommand() *cobra.Command {
	var (
		configFile string
		logLevel   string
		serverMode bool
		peerIP     string
		peerPort   int
	)

	rootCmd := &cobra.Command{
		Use:   "iperf3-controller",
		Short: "iPerf3 dual OpenWRT speed testing system",
		Long: `A network speed testing system for dual OpenWRT architecture.
Supports odd/even hour scheduling, 12 server client management,
SQLite database storage and dual-machine synchronization.`,
		Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runApplication(configFile, logLevel, serverMode, peerIP, peerPort)
		},
	}

	// Global flags
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "configs/config.yaml", "config file path")
	rootCmd.PersistentFlags().StringVarP(&logLevel, "log-level", "l", "info", "log level (debug, info, warn, error)")
	
	// Server mode flags
	rootCmd.Flags().BoolVarP(&serverMode, "server", "s", false, "run in server mode")
	rootCmd.Flags().StringVar(&peerIP, "aip", "", "peer OpenWRT IP address")
	rootCmd.Flags().IntVar(&peerPort, "ap", 55201, "peer OpenWRT port")

	return rootCmd
}

func runApplication(configFile, logLevel string, serverMode bool, peerIP string, peerPort int) error {
	// Setup logging
	level, err := logrus.ParseLevel(logLevel)
	if err != nil {
		return fmt.Errorf("invalid log level: %w", err)
	}
	logrus.SetLevel(level)
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	logrus.WithFields(logrus.Fields{
		"version":     version,
		"commit":      commit,
		"build_date":  date,
		"config_file": configFile,
		"server_mode": serverMode,
		"peer_ip":     peerIP,
		"peer_port":   peerPort,
	}).Info("Starting iPerf3 controller")

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Setup signal handling
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		logrus.WithField("signal", sig).Info("Received shutdown signal")
		cancel()
	}()

	// TODO: Initialize and start application components
	// This will be implemented in later tasks:
	// 1. Load configuration
	// 2. Initialize database
	// 3. Start client manager
	// 4. Start test coordinator
	// 5. Start scheduler
	// 6. Start sync service
	// 7. Start web server

	logrus.Info("Application framework initialized successfully")
	logrus.Info("Waiting for shutdown signal...")

	// Wait for context cancellation
	<-ctx.Done()
	logrus.Info("Shutting down gracefully...")

	return nil
}
